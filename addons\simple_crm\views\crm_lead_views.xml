<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- CRM Lead Form View Extension -->
    <record id="view_crm_lead_form_simple" model="ir.ui.view">
        <field name="name">crm.lead.form.simple</field>
        <field name="model">crm.lead</field>
        <field name="inherit_id" ref="crm.crm_lead_view_form"/>
        <field name="arch" type="xml">
            <!-- Add buttons in header -->
            <xpath expr="//sheet" position="before">
                <header>
                    <button name="action_mark_as_hot" type="object" 
                            string="Mark as HOT" class="btn-danger"/>
                    <button name="action_schedule_call" type="object" 
                            string="Schedule Call" class="btn-primary"/>
                </header>
            </xpath>
            
            <!-- Add fields after tag_ids -->
            <xpath expr="//field[@name='tag_ids']" position="after">
                <field name="lead_priority"/>
                <field name="days_old" readonly="1"/>
            </xpath>
            
            <!-- Add new tab for additional info -->
            <xpath expr="//notebook" position="inside">
                <page string="Lead Details" name="lead_details">
                    <group>
                        <group string="Lead Information">
                            <field name="lead_source_detail"/>
                            <field name="decision_maker"/>
                            <field name="budget_range"/>
                            <field name="competitor"/>
                        </group>
                    </group>
                    <group string="Next Action">
                        <field name="next_action" nolabel="1"/>
                    </group>
                </page>
            </xpath>
        </field>
    </record>

    <!-- CRM Lead Tree View Extension -->
    <record id="view_crm_lead_tree_simple" model="ir.ui.view">
        <field name="name">crm.lead.tree.simple</field>
        <field name="model">crm.lead</field>
        <field name="inherit_id" ref="crm.crm_case_tree_view_oppor"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='expected_revenue']" position="after">
                <field name="lead_priority"/>
                <field name="budget_range"/>
                <field name="days_old"/>
            </xpath>
        </field>
    </record>

    <!-- CRM Lead Search View Extension -->
    <record id="view_crm_case_filter_simple" model="ir.ui.view">
        <field name="name">crm.lead.search.simple</field>
        <field name="model">crm.lead</field>
        <field name="inherit_id" ref="crm.view_crm_case_opportunities_filter"/>
        <field name="arch" type="xml">
            <xpath expr="//filter[@name='won']" position="after">
                <separator/>
                <filter string="Hot Leads" name="hot_leads" 
                        domain="[('lead_priority', '=', 'urgent')]"/>
                <filter string="High Priority" name="high_priority" 
                        domain="[('lead_priority', 'in', ['high', 'urgent'])]"/>
                <filter string="Old Leads" name="old_leads" 
                        domain="[('days_old', '>', 30)]"/>
            </xpath>
            
            <xpath expr="//group" position="inside">
                <filter string="Priority" name="group_priority" 
                        context="{'group_by': 'lead_priority'}"/>
                <filter string="Budget Range" name="group_budget" 
                        context="{'group_by': 'budget_range'}"/>
            </xpath>
        </field>
    </record>
</odoo>
