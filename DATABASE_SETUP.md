# VIT ONE Database Setup Guide

## Tổng quan
Dự án này setup database PostgreSQL với 2 bảng chính:
- **Request**: <PERSON><PERSON><PERSON><PERSON> lý các yêu cầu từ khách hàng
- **User**: <PERSON>u<PERSON>n lý người dùng hệ thống

## Cấu trúc Database

### Bảng Request
```sql
CREATE TABLE request (
    id SERIAL PRIMARY KEY,
    request_id VARCHAR(50) UNIQUE NOT NULL,
    company_name VARCHAR(255) NOT NULL,
    company_address VARCHAR(255),
    phone_number VARCHAR(50),
    admin_name VARCHAR(255) NOT NULL,
    admin_email VARCHAR(255) NOT NULL,
    services TEXT, -- JSON stored as TEXT
    server_domain VARCHAR(255),
    request_type VARCHAR(50) DEFAULT 'new',
    request_status VARCHAR(50) DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Bảng User
```sql
CREATE TABLE "user" (
    id SERIAL PRIMARY KEY,
    user_name VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    role VARCHAR(50) DEFAULT 'user',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## Cách chạy

### 1. Khởi động containers
```bash
docker-compose up -d
```

### 2. Truy cập các services

#### Adminer (Database Management)
- URL: http://localhost:8080
- Server: db
- Username: odoo
- Password: odoo
- Database: vit_one_db

#### Odoo (nếu cần)
- URL: http://localhost:8069
- Admin password: admin123

### 3. Kiểm tra database
Sau khi chạy `docker-compose up -d`, bạn có thể:

1. Mở trình duyệt và truy cập http://localhost:8080
2. Đăng nhập Adminer với thông tin:
   - System: PostgreSQL
   - Server: db
   - Username: odoo
   - Password: odoo
   - Database: vit_one_db
3. Bạn sẽ thấy 2 bảng `request` và `user` với dữ liệu mẫu

## Dữ liệu mẫu

### Request table
- REQ001: Tech Solutions Inc
- REQ002: Digital Corp

### User table
- admin (role: admin)
- manager1 (role: manager)  
- user1 (role: user)

Tất cả user có password mặc định: `password123`

## Tính năng

### Auto-update timestamp
- Bảng `request` có trigger tự động cập nhật `updated_at` khi có thay đổi

### Indexes
- Các index được tạo để tối ưu performance cho các truy vấn thường dùng

### JSON Support
- Trường `services` trong bảng `request` hỗ trợ lưu trữ JSON data

## Lệnh hữu ích

### Xem logs
```bash
docker-compose logs -f db
docker-compose logs -f adminer
```

### Restart services
```bash
docker-compose restart
```

### Stop và xóa containers
```bash
docker-compose down
```

### Stop và xóa cả volumes (xóa data)
```bash
docker-compose down -v
```

## Troubleshooting

### Database không tạo được
- Kiểm tra logs: `docker-compose logs db`
- Đảm bảo port 5432 không bị conflict

### Adminer không kết nối được
- Đảm bảo container db đã chạy hoàn toàn
- Kiểm tra network connectivity giữa containers

### Dữ liệu bị mất
- Kiểm tra volume `odoo-db-data` có tồn tại không
- Nếu cần reset hoàn toàn: `docker-compose down -v && docker-compose up -d`
