services:
  db:
    image: postgres:15
    container_name: odoo_db
    environment:
      - POSTGRES_DB=postgres
      - POSTGRES_PASSWORD=odoo
      - POSTGRES_USER=odoo
    volumes:
      - odoo-db-data:/var/lib/postgresql/data
      - ./init-db:/docker-entrypoint-initdb.d
    restart: unless-stopped
    networks:
      - odoo-network

  adminer:
    image: adminer:latest
    container_name: odoo_adminer
    depends_on:
      - db
    ports:
      - "8081:8081"
    environment:
      - ADMINER_DEFAULT_SERVER=db
      - ADMINER_DESIGN=pepa-linha
    restart: unless-stopped
    networks:
      - odoo-network

  odoo:
    image: odoo:18.0
    container_name: odoo_app
    depends_on:
      - db
    ports:
      - "8069:8069"
      - "8072:8072"
    volumes:
      - odoo-web-data:/var/lib/odoo
      - ./addons:/mnt/extra-addons
      - ./config:/etc/odoo
    environment:
      - HOST=db
      - USER=odoo
      - PASSWORD=odoo
      - ODOO_RC=/etc/odoo/odoo.conf
    restart: unless-stopped
    networks:
      - odoo-network



volumes:
  odoo-db-data:
  odoo-web-data:

networks:
  odoo-network:
    driver: bridge 