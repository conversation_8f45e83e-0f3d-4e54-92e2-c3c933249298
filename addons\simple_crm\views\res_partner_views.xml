<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Partner Form View Extension -->
    <record id="view_partner_form_simple" model="ir.ui.view">
        <field name="name">res.partner.form.simple</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="base.view_partner_form"/>
        <field name="arch" type="xml">
            <!-- Add button in header -->
            <xpath expr="//sheet" position="before">
                <header>
                    <button name="action_view_opportunities" type="object" 
                            string="View Opportunities" class="btn-primary"
                            attrs="{'invisible': [('total_opportunities', '=', 0)]}"/>
                </header>
            </xpath>
            
            <!-- Add fields after category -->
            <xpath expr="//field[@name='category_id']" position="after">
                <field name="customer_type"/>
                <field name="customer_rating"/>
                <field name="total_opportunities" readonly="1"/>
            </xpath>
            
            <!-- Add new tab for additional info -->
            <xpath expr="//notebook" position="inside">
                <page string="Customer Info" name="customer_info">
                    <group>
                        <group string="Contact Management">
                            <field name="last_contact_date"/>
                        </group>
                    </group>
                    <group string="Notes">
                        <field name="notes" nolabel="1"/>
                    </group>
                </page>
            </xpath>
        </field>
    </record>

    <!-- Partner Tree View Extension -->
    <record id="view_partner_tree_simple" model="ir.ui.view">
        <field name="name">res.partner.tree.simple</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="base.view_partner_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='display_name']" position="after">
                <field name="customer_type"/>
                <field name="customer_rating"/>
                <field name="total_opportunities"/>
            </xpath>
        </field>
    </record>

    <!-- Partner Search View Extension -->
    <record id="view_res_partner_filter_simple" model="ir.ui.view">
        <field name="name">res.partner.search.simple</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="base.view_res_partner_filter"/>
        <field name="arch" type="xml">
            <xpath expr="//filter[@name='supplier']" position="after">
                <separator/>
                <filter string="VIP Customers" name="vip_customers" 
                        domain="[('customer_type', '=', 'vip')]"/>
                <filter string="Prospects" name="prospects" 
                        domain="[('customer_type', '=', 'prospect')]"/>
                <filter string="High Rated" name="high_rated" 
                        domain="[('customer_rating', 'in', ['4', '5'])]"/>
            </xpath>
            
            <xpath expr="//group" position="inside">
                <filter string="Customer Type" name="group_customer_type" 
                        context="{'group_by': 'customer_type'}"/>
                <filter string="Rating" name="group_rating" 
                        context="{'group_by': 'customer_rating'}"/>
            </xpath>
        </field>
    </record>
</odoo>
