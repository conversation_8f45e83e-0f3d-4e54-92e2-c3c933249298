from odoo import models, fields, api


class ResPartner(models.Model):
    _inherit = 'res.partner'

    # Simple additional fields for contacts
    customer_type = fields.Selection([
        ('regular', 'Regular Customer'),
        ('vip', 'VIP Customer'),
        ('prospect', 'Prospect'),
    ], string='Customer Type', default='regular')
    
    customer_rating = fields.Selection([
        ('1', '1 Star'),
        ('2', '2 Stars'),
        ('3', '3 Stars'),
        ('4', '4 Stars'),
        ('5', '5 Stars'),
    ], string='Customer Rating')
    
    last_contact_date = fields.Date(
        string='Last Contact Date',
        help='Last time we contacted this customer'
    )
    
    notes = fields.Text(string='Internal Notes')
    
    # Computed field for total opportunities
    total_opportunities = fields.Integer(
        string='Total Opportunities',
        compute='_compute_opportunity_count',
        store=True
    )
    
    @api.depends('opportunity_ids')
    def _compute_opportunity_count(self):
        for partner in self:
            partner.total_opportunities = len(partner.opportunity_ids)
    
    def action_view_opportunities(self):
        """Open opportunities for this partner"""
        return {
            'type': 'ir.actions.act_window',
            'name': 'Opportunities',
            'res_model': 'crm.lead',
            'view_mode': 'tree,form',
            'domain': [('partner_id', '=', self.id)],
            'context': {'default_partner_id': self.id}
        }
